# Make Data Count Deployment Guide for DRH

## Overview

This guide covers the deployment aspects of Make Data Count integration in the Diabetes Research Hub project, including containerization, environment configuration, and monitoring setup.

## Prerequisites

- Docker and Docker Compose
- PostgreSQL database
- DataCite account with API credentials
- SSL certificates for HTTPS endpoints
- Monitoring infrastructure (Prometheus, Grafana)

## Environment Configuration

### Production Environment Variables

Create a `.env.production` file:

```bash
# Make Data Count Configuration
MDC_ENABLED=true
DATACITE_API_URL=https://api.datacite.org
DATACITE_USERNAME=your_production_username
DATACITE_PASSWORD=your_production_password
DATACITE_REPOSITORY_ID=drh.diabetes-research
DATACITE_PREFIX=10.5555

# Database Configuration
POSTGRES_HOST=postgres-prod.your-domain.com
POSTGRES_PORT=5432
POSTGRES_DB=drh_production
POSTGRES_USER=drh_user
POSTGRES_PASSWORD=secure_password

# OpenTelemetry Configuration
OTEL_EXPORTER_OTLP_ENDPOINT=https://your-observability-endpoint.com
OTEL_EXPORTER_OTLP_HEADERS=authorization=Bearer your_token
OTEL_SERVICE_NAME=drh-make-data-count
OTEL_RESOURCE_ATTRIBUTES=service.version=1.0.0,deployment.environment=production

# Security Configuration
JWT_SECRET=your_jwt_secret_key
CORS_ALLOWED_ORIGINS=https://your-drh-domain.com,https://api.your-drh-domain.com

# External Services
CROSSREF_API_URL=https://api.crossref.org
PUBMED_API_URL=https://eutils.ncbi.nlm.nih.gov/entrez/eutils
SEMANTIC_SCHOLAR_API_URL=https://api.semanticscholar.org

# Caching Configuration
REDIS_HOST=redis-prod.your-domain.com
REDIS_PORT=6379
REDIS_PASSWORD=redis_password

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_FILE=/var/log/drh/make-data-count.log
```

### Staging Environment Variables

Create a `.env.staging` file:

```bash
# Make Data Count Configuration (Test Environment)
MDC_ENABLED=true
DATACITE_API_URL=https://api.test.datacite.org
DATACITE_USERNAME=your_test_username
DATACITE_PASSWORD=your_test_password
DATACITE_REPOSITORY_ID=drh.diabetes-research.test
DATACITE_PREFIX=10.5072

# Database Configuration
POSTGRES_HOST=postgres-staging.your-domain.com
POSTGRES_PORT=5432
POSTGRES_DB=drh_staging
POSTGRES_USER=drh_staging_user
POSTGRES_PASSWORD=staging_password

# Reduced logging for staging
LOG_LEVEL=DEBUG
```

## Docker Configuration

### Dockerfile Extensions

Add to your existing `hub-prime/Dockerfile`:

```dockerfile
# Add Make Data Count specific dependencies
FROM openjdk:21-jdk-slim as builder

# Copy Make Data Count configuration
COPY src/main/resources/make-data-count/ /app/make-data-count/

# Install additional tools for metrics processing
RUN apt-get update && apt-get install -y \
    curl \
    jq \
    && rm -rf /var/lib/apt/lists/*

# Add health check for Make Data Count services
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/actuator/health/make-data-count || exit 1

# Create volume for metrics data
VOLUME ["/var/log/drh", "/var/cache/drh-metrics"]

# Set environment variables
ENV MDC_ENABLED=true
ENV JAVA_OPTS="-Xmx2g -Xms1g -XX:+UseG1GC"
```

### Docker Compose Configuration

Create `docker-compose.make-data-count.yml`:

```yaml
version: '3.8'

services:
  drh-hub:
    build:
      context: ./hub-prime
      dockerfile: Dockerfile
    environment:
      - MDC_ENABLED=${MDC_ENABLED}
      - DATACITE_API_URL=${DATACITE_API_URL}
      - DATACITE_USERNAME=${DATACITE_USERNAME}
      - DATACITE_PASSWORD=${DATACITE_PASSWORD}
      - DATACITE_REPOSITORY_ID=${DATACITE_REPOSITORY_ID}
      - DATACITE_PREFIX=${DATACITE_PREFIX}
    volumes:
      - drh-logs:/var/log/drh
      - drh-metrics-cache:/var/cache/drh-metrics
    depends_on:
      - postgres
      - redis
    networks:
      - drh-network

  postgres:
    image: postgres:16
    environment:
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./udi-prime/src/main/postgres:/docker-entrypoint-initdb.d
    networks:
      - drh-network

  redis:
    image: redis:7-alpine
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis-data:/data
    networks:
      - drh-network

  # Metrics processing service
  metrics-processor:
    build:
      context: ./metrics-processor
      dockerfile: Dockerfile
    environment:
      - POSTGRES_HOST=postgres
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - REDIS_HOST=redis
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    depends_on:
      - postgres
      - redis
    networks:
      - drh-network

  # Citation detection service
  citation-detector:
    build:
      context: ./citation-detector
      dockerfile: Dockerfile
    environment:
      - CROSSREF_API_URL=${CROSSREF_API_URL}
      - PUBMED_API_URL=${PUBMED_API_URL}
      - POSTGRES_HOST=postgres
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    depends_on:
      - postgres
    networks:
      - drh-network

  # Nginx reverse proxy
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - drh-logs:/var/log/nginx
    depends_on:
      - drh-hub
    networks:
      - drh-network

volumes:
  postgres-data:
  redis-data:
  drh-logs:
  drh-metrics-cache:

networks:
  drh-network:
    driver: bridge
```

## Kubernetes Deployment

### Namespace Configuration

Create `k8s/namespace.yaml`:

```yaml
apiVersion: v1
kind: Namespace
metadata:
  name: drh-make-data-count
  labels:
    name: drh-make-data-count
    environment: production
```

### ConfigMap for Make Data Count

Create `k8s/configmap.yaml`:

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: make-data-count-config
  namespace: drh-make-data-count
data:
  application.yml: |
    org:
      diabetestechnology:
        drh:
          make-data-count:
            enabled: true
            datacite:
              api-url: https://api.datacite.org
              repository-id: drh.diabetes-research
              prefix: "10.5555"
            metrics:
              collection-enabled: true
              collection-interval: PT1H
              reporting-enabled: true
            citations:
              detection-enabled: true
              detection-interval: P1D
              sources:
                - crossref
                - pubmed
                - datacite
```

### Secret Configuration

Create `k8s/secrets.yaml`:

```yaml
apiVersion: v1
kind: Secret
metadata:
  name: make-data-count-secrets
  namespace: drh-make-data-count
type: Opaque
data:
  datacite-username: <base64-encoded-username>
  datacite-password: <base64-encoded-password>
  postgres-password: <base64-encoded-password>
  redis-password: <base64-encoded-password>
  jwt-secret: <base64-encoded-jwt-secret>
```

### Deployment Configuration

Create `k8s/deployment.yaml`:

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: drh-make-data-count
  namespace: drh-make-data-count
spec:
  replicas: 3
  selector:
    matchLabels:
      app: drh-make-data-count
  template:
    metadata:
      labels:
        app: drh-make-data-count
    spec:
      containers:
      - name: drh-hub
        image: drh/hub-prime:latest
        ports:
        - containerPort: 8080
        env:
        - name: MDC_ENABLED
          value: "true"
        - name: DATACITE_USERNAME
          valueFrom:
            secretKeyRef:
              name: make-data-count-secrets
              key: datacite-username
        - name: DATACITE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: make-data-count-secrets
              key: datacite-password
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: make-data-count-secrets
              key: postgres-password
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
        - name: logs-volume
          mountPath: /var/log/drh
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /actuator/health/readiness
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
      volumes:
      - name: config-volume
        configMap:
          name: make-data-count-config
      - name: logs-volume
        persistentVolumeClaim:
          claimName: drh-logs-pvc
```

## Monitoring and Observability

### Prometheus Metrics Configuration

Add to `prometheus.yml`:

```yaml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'drh-make-data-count'
    static_configs:
      - targets: ['drh-hub:8080']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 30s
    
  - job_name: 'drh-metrics-processor'
    static_configs:
      - targets: ['metrics-processor:9090']
    scrape_interval: 60s

rule_files:
  - "drh_make_data_count_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
```

### Grafana Dashboard Configuration

Create `grafana/drh-make-data-count-dashboard.json`:

```json
{
  "dashboard": {
    "title": "DRH Make Data Count Metrics",
    "panels": [
      {
        "title": "DOI Minting Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(drh_doi_minting_total[5m])",
            "legendFormat": "DOIs per second"
          }
        ]
      },
      {
        "title": "Dataset Views",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(drh_dataset_views_total[5m])",
            "legendFormat": "Views per second"
          }
        ]
      },
      {
        "title": "Citation Detection",
        "type": "graph",
        "targets": [
          {
            "expr": "drh_citations_detected_total",
            "legendFormat": "Total Citations"
          }
        ]
      }
    ]
  }
}
```

### Alert Rules

Create `prometheus/drh_make_data_count_rules.yml`:

```yaml
groups:
  - name: drh_make_data_count
    rules:
      - alert: DOIMintingFailure
        expr: rate(drh_doi_minting_failures_total[5m]) > 0.1
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "High DOI minting failure rate"
          description: "DOI minting failure rate is {{ $value }} per second"

      - alert: CitationDetectionDown
        expr: up{job="citation-detector"} == 0
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "Citation detection service is down"

      - alert: HighUsageMetricsLatency
        expr: histogram_quantile(0.95, rate(drh_usage_metrics_duration_seconds_bucket[5m])) > 1
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "High latency in usage metrics collection"
```

## Backup and Recovery

### Database Backup Script

Create `scripts/backup-make-data-count.sh`:

```bash
#!/bin/bash

# Make Data Count specific backup script
BACKUP_DIR="/backups/make-data-count"
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup directory
mkdir -p $BACKUP_DIR

# Backup Make Data Count tables
pg_dump -h $POSTGRES_HOST -U $POSTGRES_USER -d $POSTGRES_DB \
  --table=drh_stateful_research_study.dataset_dois \
  --table=drh_stateful_research_study.data_citations \
  --table=drh_stateful_research_study.dataset_usage_metrics \
  --table=drh_stateful_research_study.dataset_impact_metrics \
  > $BACKUP_DIR/make_data_count_backup_$DATE.sql

# Compress backup
gzip $BACKUP_DIR/make_data_count_backup_$DATE.sql

# Upload to cloud storage (example with AWS S3)
aws s3 cp $BACKUP_DIR/make_data_count_backup_$DATE.sql.gz \
  s3://drh-backups/make-data-count/

# Clean up old backups (keep last 30 days)
find $BACKUP_DIR -name "*.sql.gz" -mtime +30 -delete

echo "Backup completed: make_data_count_backup_$DATE.sql.gz"
```

### Recovery Procedures

Create `scripts/restore-make-data-count.sh`:

```bash
#!/bin/bash

# Make Data Count recovery script
BACKUP_FILE=$1

if [ -z "$BACKUP_FILE" ]; then
    echo "Usage: $0 <backup_file>"
    exit 1
fi

# Download from cloud storage if needed
if [[ $BACKUP_FILE == s3://* ]]; then
    aws s3 cp $BACKUP_FILE ./
    BACKUP_FILE=$(basename $BACKUP_FILE)
fi

# Decompress if needed
if [[ $BACKUP_FILE == *.gz ]]; then
    gunzip $BACKUP_FILE
    BACKUP_FILE=${BACKUP_FILE%.gz}
fi

# Restore database
psql -h $POSTGRES_HOST -U $POSTGRES_USER -d $POSTGRES_DB < $BACKUP_FILE

echo "Recovery completed from: $BACKUP_FILE"
```

## Performance Optimization

### Database Optimization

```sql
-- Create additional indexes for performance
CREATE INDEX CONCURRENTLY idx_usage_metrics_doi_date 
ON drh_stateful_research_study.dataset_usage_metrics(dataset_doi, DATE(timestamp));

CREATE INDEX CONCURRENTLY idx_citations_detected_date 
ON drh_stateful_research_study.data_citations(detected_at);

-- Partition usage metrics table by date
CREATE TABLE drh_stateful_research_study.dataset_usage_metrics_2024 
PARTITION OF drh_stateful_research_study.dataset_usage_metrics 
FOR VALUES FROM ('2024-01-01') TO ('2025-01-01');
```

### Caching Configuration

Add to `application.yml`:

```yaml
spring:
  cache:
    type: redis
    redis:
      time-to-live: 3600000 # 1 hour
      cache-null-values: false
  data:
    redis:
      host: ${REDIS_HOST}
      port: ${REDIS_PORT}
      password: ${REDIS_PASSWORD}
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
```

## Security Considerations

### API Security

```yaml
# Add to application.yml
security:
  oauth2:
    resourceserver:
      jwt:
        issuer-uri: https://your-auth-provider.com
  
management:
    endpoints:
      web:
        exposure:
          include: health,info,metrics,prometheus
        base-path: /actuator
    endpoint:
      health:
        show-details: when-authorized
```

### Network Security

```yaml
# Network policies for Kubernetes
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: make-data-count-network-policy
  namespace: drh-make-data-count
spec:
  podSelector:
    matchLabels:
      app: drh-make-data-count
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: drh-ingress
    ports:
    - protocol: TCP
      port: 8080
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: drh-database
    ports:
    - protocol: TCP
      port: 5432
```

## Deployment Checklist

### Pre-deployment
- [ ] Database migrations tested
- [ ] Environment variables configured
- [ ] SSL certificates installed
- [ ] DataCite credentials verified
- [ ] Monitoring dashboards created
- [ ] Backup procedures tested

### Deployment
- [ ] Deploy database changes
- [ ] Deploy application updates
- [ ] Verify health checks
- [ ] Test API endpoints
- [ ] Validate metrics collection
- [ ] Check log aggregation

### Post-deployment
- [ ] Monitor error rates
- [ ] Verify DOI minting
- [ ] Test citation detection
- [ ] Validate usage tracking
- [ ] Check performance metrics
- [ ] Confirm backup execution

---

This deployment guide provides comprehensive instructions for deploying Make Data Count integration in production environments, ensuring reliability, security, and observability.
