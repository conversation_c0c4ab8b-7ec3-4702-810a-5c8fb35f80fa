# Make Data Count Implementation Guide for DRH

## Quick Start Implementation

This guide provides step-by-step instructions for implementing Make Data Count metrics in the Diabetes Research Hub project.

## Prerequisites

- Java 21+ with Spring Boot 3.3+
- PostgreSQL database access
- DataCite account (for DOI minting)
- OpenTelemetry configured (already present in DRH)

## Step 1: Database Schema Extensions

### Create New Tables for Make Data Count

```sql
-- Add to your migration files in udi-prime/src/main/postgres/ingestion-center/migrations/

-- Dataset DOI tracking
CREATE TABLE IF NOT EXISTS drh_stateful_research_study.dataset_dois (
    id SERIAL PRIMARY KEY,
    study_id VARCHAR(255) NOT NULL,
    participant_id VARCHAR(255),
    dataset_type VARCHAR(100) NOT NULL, -- 'study', 'participant', 'aggregate'
    doi VARCHAR(255) UNIQUE NOT NULL,
    title VARCHAR(500) NOT NULL,
    description TEXT,
    metadata JSONB,
    status VARCHAR(50) DEFAULT 'draft', -- 'draft', 'published', 'updated'
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    created_by <PERSON><PERSON><PERSON><PERSON>(255),
    FOREIGN KEY (study_id) REFERENCES drh_stateful_research_study.research_study(study_id)
);

-- Data citations tracking
CREATE TABLE IF NOT EXISTS drh_stateful_research_study.data_citations (
    id SERIAL PRIMARY KEY,
    dataset_doi VARCHAR(255) NOT NULL,
    citing_work_doi VARCHAR(255),
    citing_work_title VARCHAR(500),
    citing_work_authors TEXT,
    citation_type VARCHAR(100), -- 'direct', 'indirect', 'derived'
    citation_context TEXT,
    detected_at TIMESTAMP DEFAULT NOW(),
    verified BOOLEAN DEFAULT FALSE,
    source VARCHAR(100), -- 'crossref', 'pubmed', 'manual'
    FOREIGN KEY (dataset_doi) REFERENCES drh_stateful_research_study.dataset_dois(doi)
);

-- Usage metrics (COUNTER-compliant)
CREATE TABLE IF NOT EXISTS drh_stateful_research_study.dataset_usage_metrics (
    id SERIAL PRIMARY KEY,
    dataset_doi VARCHAR(255) NOT NULL,
    metric_type VARCHAR(50) NOT NULL, -- 'view', 'download', 'analysis'
    user_agent TEXT,
    ip_address INET,
    session_id VARCHAR(255),
    user_id VARCHAR(255),
    institution_id VARCHAR(255),
    country_code VARCHAR(3),
    access_method VARCHAR(100), -- 'web', 'api', 'direct'
    file_format VARCHAR(50),
    timestamp TIMESTAMP DEFAULT NOW(),
    processing_date DATE DEFAULT CURRENT_DATE,
    FOREIGN KEY (dataset_doi) REFERENCES drh_stateful_research_study.dataset_dois(doi)
);

-- Impact metrics aggregation
CREATE TABLE IF NOT EXISTS drh_stateful_research_study.dataset_impact_metrics (
    id SERIAL PRIMARY KEY,
    dataset_doi VARCHAR(255) NOT NULL,
    metric_period VARCHAR(20) NOT NULL, -- 'daily', 'monthly', 'yearly'
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,
    view_count INTEGER DEFAULT 0,
    download_count INTEGER DEFAULT 0,
    citation_count INTEGER DEFAULT 0,
    unique_users INTEGER DEFAULT 0,
    unique_institutions INTEGER DEFAULT 0,
    countries_reached INTEGER DEFAULT 0,
    calculated_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(dataset_doi, metric_period, period_start),
    FOREIGN KEY (dataset_doi) REFERENCES drh_stateful_research_study.dataset_dois(doi)
);

-- Create indexes for performance
CREATE INDEX idx_dataset_dois_study_id ON drh_stateful_research_study.dataset_dois(study_id);
CREATE INDEX idx_data_citations_dataset_doi ON drh_stateful_research_study.data_citations(dataset_doi);
CREATE INDEX idx_usage_metrics_doi_timestamp ON drh_stateful_research_study.dataset_usage_metrics(dataset_doi, timestamp);
CREATE INDEX idx_impact_metrics_doi_period ON drh_stateful_research_study.dataset_impact_metrics(dataset_doi, metric_period, period_start);
```

## Step 2: Configuration Setup

### Add DataCite Configuration

Add to `hub-prime/src/main/resources/application.yml`:

```yaml
org:
  diabetestechnology:
    drh:
      make-data-count:
        enabled: ${MDC_ENABLED:true}
        datacite:
          api-url: ${DATACITE_API_URL:https://api.test.datacite.org}
          username: ${DATACITE_USERNAME:}
          password: ${DATACITE_PASSWORD:}
          repository-id: ${DATACITE_REPOSITORY_ID:drh.diabetes-research}
          prefix: ${DATACITE_PREFIX:10.5555}
        metrics:
          collection-enabled: true
          collection-interval: PT1H
          reporting-enabled: true
          counter-compliant: true
        citations:
          detection-enabled: true
          detection-interval: P1D
          sources:
            - crossref
            - pubmed
            - datacite
```

### Environment Variables

Add to your `.envrc` file:

```bash
# Make Data Count Configuration
export MDC_ENABLED=true
export DATACITE_API_URL=https://api.test.datacite.org  # Use https://api.datacite.org for production
export DATACITE_USERNAME=your_datacite_username
export DATACITE_PASSWORD=your_datacite_password
export DATACITE_REPOSITORY_ID=drh.diabetes-research
export DATACITE_PREFIX=10.5555  # Your assigned DOI prefix
```

## Step 3: Core Service Implementation

### DataCite Integration Service

Create `hub-prime/src/main/java/org/diabetestechnology/drh/service/http/hub/prime/service/DataCiteService.java`:

```java
@Service
@Slf4j
public class DataCiteService {
    
    @Value("${org.diabetestechnology.drh.make-data-count.datacite.api-url}")
    private String dataciteApiUrl;
    
    @Value("${org.diabetestechnology.drh.make-data-count.datacite.username}")
    private String username;
    
    @Value("${org.diabetestechnology.drh.make-data-count.datacite.password}")
    private String password;
    
    @Value("${org.diabetestechnology.drh.make-data-count.datacite.prefix}")
    private String doiPrefix;
    
    private final RestTemplate restTemplate;
    private final DSLContext dsl;
    
    public DataCiteService(RestTemplate restTemplate, DSLContext dsl) {
        this.restTemplate = restTemplate;
        this.dsl = dsl;
    }
    
    public String mintDOI(String studyId, String title, String description, Map<String, Object> metadata) {
        try {
            String doi = generateDOI(studyId);
            DataCiteMetadata dataciteMetadata = buildMetadata(doi, title, description, metadata);
            
            // Submit to DataCite
            submitToDataCite(doi, dataciteMetadata);
            
            // Store in database
            storeDOI(studyId, doi, title, description, metadata);
            
            log.info("Successfully minted DOI: {} for study: {}", doi, studyId);
            return doi;
            
        } catch (Exception e) {
            log.error("Failed to mint DOI for study: {}", studyId, e);
            throw new RuntimeException("DOI minting failed", e);
        }
    }
    
    private String generateDOI(String studyId) {
        return doiPrefix + "/drh-study-" + studyId.toLowerCase() + "-" + System.currentTimeMillis();
    }
    
    private void storeDOI(String studyId, String doi, String title, String description, Map<String, Object> metadata) {
        dsl.insertInto(table("drh_stateful_research_study.dataset_dois"))
           .set(field("study_id"), studyId)
           .set(field("dataset_type"), "study")
           .set(field("doi"), doi)
           .set(field("title"), title)
           .set(field("description"), description)
           .set(field("metadata"), JSONB.valueOf(new ObjectMapper().writeValueAsString(metadata)))
           .set(field("status"), "published")
           .set(field("created_by"), getCurrentUser())
           .execute();
    }
    
    // Additional methods for DataCite API interaction...
}
```

### Usage Metrics Collector

Create `hub-prime/src/main/java/org/diabetestechnology/drh/service/http/hub/prime/service/UsageMetricsCollector.java`:

```java
@Service
@Slf4j
public class UsageMetricsCollector {
    
    private final DSLContext dsl;
    private final GeoLocationService geoLocationService;
    
    public UsageMetricsCollector(DSLContext dsl, GeoLocationService geoLocationService) {
        this.dsl = dsl;
        this.geoLocationService = geoLocationService;
    }
    
    @Async
    public void recordDatasetView(String doi, HttpServletRequest request, String userId) {
        try {
            String userAgent = request.getHeader("User-Agent");
            String ipAddress = getClientIpAddress(request);
            String sessionId = request.getSession().getId();
            String countryCode = geoLocationService.getCountryCode(ipAddress);
            
            dsl.insertInto(table("drh_stateful_research_study.dataset_usage_metrics"))
               .set(field("dataset_doi"), doi)
               .set(field("metric_type"), "view")
               .set(field("user_agent"), userAgent)
               .set(field("ip_address"), ipAddress)
               .set(field("session_id"), sessionId)
               .set(field("user_id"), userId)
               .set(field("country_code"), countryCode)
               .set(field("access_method"), "web")
               .execute();
               
            log.debug("Recorded dataset view for DOI: {}", doi);
            
        } catch (Exception e) {
            log.error("Failed to record dataset view for DOI: {}", doi, e);
        }
    }
    
    @Async
    public void recordDatasetDownload(String doi, String format, HttpServletRequest request, String userId) {
        try {
            String userAgent = request.getHeader("User-Agent");
            String ipAddress = getClientIpAddress(request);
            String sessionId = request.getSession().getId();
            String countryCode = geoLocationService.getCountryCode(ipAddress);
            
            dsl.insertInto(table("drh_stateful_research_study.dataset_usage_metrics"))
               .set(field("dataset_doi"), doi)
               .set(field("metric_type"), "download")
               .set(field("user_agent"), userAgent)
               .set(field("ip_address"), ipAddress)
               .set(field("session_id"), sessionId)
               .set(field("user_id"), userId)
               .set(field("country_code"), countryCode)
               .set(field("access_method"), "web")
               .set(field("file_format"), format)
               .execute();
               
            log.debug("Recorded dataset download for DOI: {} in format: {}", doi, format);
            
        } catch (Exception e) {
            log.error("Failed to record dataset download for DOI: {}", doi, e);
        }
    }
    
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        return request.getRemoteAddr();
    }
}
```

## Step 4: Extend Existing Controllers

### Modify Study Controllers to Track Usage

Add to existing study controllers:

```java
@Autowired
private UsageMetricsCollector usageMetricsCollector;

@Autowired
private DataCiteService dataCiteService;

@GetMapping("/studies/{studyId}")
public String viewStudy(@PathVariable String studyId, HttpServletRequest request, Model model) {
    // Existing study loading logic...
    
    // Record dataset view if DOI exists
    String doi = getStudyDOI(studyId);
    if (doi != null) {
        String userId = getCurrentUserId();
        usageMetricsCollector.recordDatasetView(doi, request, userId);
    }
    
    // Add impact metrics to model
    model.addAttribute("impactMetrics", getStudyImpactMetrics(studyId));
    
    return "page/study-detail";
}

@GetMapping("/studies/{studyId}/download")
public ResponseEntity<Resource> downloadStudyData(@PathVariable String studyId, 
                                                 @RequestParam String format,
                                                 HttpServletRequest request) {
    // Existing download logic...
    
    // Record dataset download
    String doi = getStudyDOI(studyId);
    if (doi != null) {
        String userId = getCurrentUserId();
        usageMetricsCollector.recordDatasetDownload(doi, format, request, userId);
    }
    
    return ResponseEntity.ok(resource);
}
```

## Step 5: Create Impact Metrics Dashboard

### Add Web Component for Metrics Display

Create `web-components/src/components/impact-metrics-dashboard.ts`:

```typescript
import { LitElement, html, css } from 'lit';
import { customElement, property } from 'lit/decorators.js';

@customElement('impact-metrics-dashboard')
export class ImpactMetricsDashboard extends LitElement {
  @property({ type: Object }) metrics: any = {};
  @property({ type: String }) studyId = '';

  static styles = css`
    .metrics-container {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;
      margin: 1rem 0;
    }
    
    .metric-card {
      background: white;
      border-radius: 8px;
      padding: 1rem;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      text-align: center;
    }
    
    .metric-value {
      font-size: 2rem;
      font-weight: bold;
      color: #2563eb;
    }
    
    .metric-label {
      font-size: 0.875rem;
      color: #6b7280;
      margin-top: 0.5rem;
    }
  `;

  render() {
    return html`
      <div class="metrics-container">
        <div class="metric-card">
          <div class="metric-value">${this.metrics.viewCount || 0}</div>
          <div class="metric-label">Dataset Views</div>
        </div>
        
        <div class="metric-card">
          <div class="metric-value">${this.metrics.downloadCount || 0}</div>
          <div class="metric-label">Downloads</div>
        </div>
        
        <div class="metric-card">
          <div class="metric-value">${this.metrics.citationCount || 0}</div>
          <div class="metric-label">Citations</div>
        </div>
        
        <div class="metric-card">
          <div class="metric-value">${this.metrics.uniqueUsers || 0}</div>
          <div class="metric-label">Unique Users</div>
        </div>
        
        <div class="metric-card">
          <div class="metric-value">${this.metrics.countriesReached || 0}</div>
          <div class="metric-label">Countries Reached</div>
        </div>
      </div>
    `;
  }

  connectedCallback() {
    super.connectedCallback();
    this.loadMetrics();
  }

  private async loadMetrics() {
    try {
      const response = await fetch(`/api/metrics/study/${this.studyId}/impact`);
      this.metrics = await response.json();
    } catch (error) {
      console.error('Failed to load impact metrics:', error);
    }
  }
}
```

## Step 6: Testing Implementation

### Create Test Cases

Create `hub-prime/src/test/java/org/diabetestechnology/drh/service/http/hub/prime/service/DataCiteServiceTest.java`:

```java
@SpringBootTest
@TestMethodOrder(OrderAnnotation.class)
class DataCiteServiceTest {
    
    @Autowired
    private DataCiteService dataCiteService;
    
    @Test
    @Order(1)
    void testMintDOI() {
        String studyId = "test-study-001";
        String title = "Test Diabetes Study Dataset";
        String description = "Test dataset for diabetes research";
        Map<String, Object> metadata = Map.of(
            "creator", "Test Researcher",
            "publicationYear", "2024",
            "resourceType", "Dataset"
        );
        
        String doi = dataCiteService.mintDOI(studyId, title, description, metadata);
        
        assertThat(doi).isNotNull();
        assertThat(doi).startsWith("10.5555/drh-study-");
    }
    
    @Test
    @Order(2)
    void testUsageMetricsCollection() {
        // Test usage metrics recording
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.addHeader("User-Agent", "Mozilla/5.0 Test Browser");
        request.setRemoteAddr("***********");
        
        usageMetricsCollector.recordDatasetView("10.5555/test-doi", request, "test-user");
        
        // Verify metrics were recorded
        // Add assertions based on your database verification logic
    }
}
```

## Step 7: Deployment Checklist

### Pre-deployment Steps

1. **Database Migration**: Run the schema creation scripts
2. **Configuration**: Set all required environment variables
3. **DataCite Account**: Ensure DataCite credentials are valid
4. **Testing**: Run all test cases
5. **Monitoring**: Verify OpenTelemetry metrics are being collected

### Post-deployment Verification

1. **DOI Minting**: Test DOI creation for a sample study
2. **Usage Tracking**: Verify metrics are being recorded
3. **Dashboard Display**: Check impact metrics display correctly
4. **API Endpoints**: Test all new API endpoints
5. **Performance**: Monitor system performance impact

## Monitoring and Maintenance

### Key Metrics to Monitor

- DOI minting success rate
- Usage metrics collection rate
- Citation detection accuracy
- API response times
- Database performance

### Regular Maintenance Tasks

- Monthly citation detection runs
- Quarterly impact metrics aggregation
- Annual DataCite metadata updates
- Regular backup of metrics data

## Troubleshooting

### Common Issues

1. **DOI Minting Failures**: Check DataCite credentials and API connectivity
2. **Missing Usage Metrics**: Verify request interceptors are working
3. **Citation Detection Issues**: Check external API connectivity
4. **Performance Problems**: Review database indexes and query optimization

### Support Resources

- DataCite Support: <EMAIL>
- Make Data Count Community: https://makedatacount.org/get-involved/
- DRH Development Team: Internal support channels

---

This implementation guide provides a practical roadmap for integrating Make Data Count metrics into the DRH project. Follow the steps sequentially and test thoroughly at each stage.
